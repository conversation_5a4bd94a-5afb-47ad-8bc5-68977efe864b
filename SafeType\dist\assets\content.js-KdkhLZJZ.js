(function(){console.log("SafeType content script loaded");let f=null,u=null,a=null,p=null,l=null;function y(){["textarea",'input[type="text"]','input[type="email"]','[contenteditable="true"]','[role="textbox"]',".editable",'[data-testid*="compose"]','[data-testid*="message"]','div[contenteditable="true"][role="textbox"]','div[contenteditable="true"][aria-label*="Message"]','div[contenteditable="true"][aria-label*="message"]','div[contenteditable="true"][aria-label*="compose"]','div[contenteditable="true"][aria-label*="reply"]','div[contenteditable="true"][aria-label*="forward"]','div[contenteditable="true"][data-tab]','div[contenteditable="true"][role="textbox"][spellcheck="true"]','div[contenteditable="true"][aria-label*="Tweet"]','div[contenteditable="true"][aria-label*="Post"]','div[contenteditable="true"][aria-label*="Write"]','div[contenteditable="true"][role="textbox"][aria-multiline="true"]','div[contenteditable="true"][role="textbox"][aria-label*="Message"]','div[contenteditable="true"][data-qa="message_input"]','div[contenteditable="true"][aria-label*="Write"]','div[contenteditable="true"][role="textbox"][aria-describedby]'].forEach(o=>{document.querySelectorAll(o).forEach(t=>{t.hasAttribute("data-safetype-monitored")||(t.setAttribute("data-safetype-monitored","true"),t.addEventListener("input",C),t.addEventListener("focus",M),t.addEventListener("blur",z))})})}function M(e){f=e.target}function z(e){f===e.target&&(f=null,r())}function C(e){const o=e.target,t=I(o);u&&clearTimeout(u),t.length>10?u=setTimeout(()=>{N(t,o)},2e3):r()}function I(e){return e.tagName==="TEXTAREA"||e.tagName==="INPUT"?e.value:e.contentEditable==="true"?e.innerText||e.textContent:""}async function N(e,o){try{const t=await chrome.storage.sync.get(["toneDetection","safeFilter","zenMode"]);if(t.toneDetection===!1)return;if(t.safeFilter!==!1){const i=await chrome.runtime.sendMessage({type:"CHECK_SAFE_CONTENT",payload:e});if(i.success&&i.safetyCheck&&!i.safetyCheck.is_safe){_(o,i.safetyCheck),b("Filtered",x(),"🚫");return}}const n=await chrome.runtime.sendMessage({type:"ANALYZE_CONFIDENCE",payload:e});if(n.success&&n.analysis){const{confidence_score:i,needs_rewriting:s,explanation:w,suggested_improvements:E,mood:T,emotional_tone:g,writing_style:S}=n.analysis,c=T||(i>=7?"Confident":i>=5?"Neutral":"Anxious"),k=L(c);b(c,x(),k),t.zenMode!==!0?i<6&&s?F(o,{score:i,explanation:w,improvements:E,originalText:e,mood:c,emotional_tone:g,writing_style:S}):i>=8?A(o,{score:i,mood:c,emotional_tone:g}):r():r()}}catch(t){console.error("SafeType: Error analyzing confidence:",t)}}function L(e){return{Confident:"😊",Neutral:"😐",Anxious:"😰",Excited:"🤩",Frustrated:"😤",Sad:"😢",Happy:"😄",Filtered:"🚫"}[e]||"😐"}function x(){const e=window.location.hostname;return e.includes("gmail.com")?"Gmail":e.includes("discord.com")?"Discord":e.includes("slack.com")?"Slack":e.includes("twitter.com")||e.includes("x.com")?"Twitter":e.includes("linkedin.com")?"LinkedIn":e.includes("facebook.com")?"Facebook":"Web"}function b(e,o,t){const n=new Date;chrome.storage.local.get(["moodHistory"],function(i){const s=i.moodHistory||[];s.unshift({mood:e,source:o,emoji:t,timestamp:n.toISOString()}),s.length>20&&s.splice(20),chrome.storage.local.set({moodHistory:s})})}document.addEventListener("mouseup",function(){const e=window.getSelection().toString().trim();e.length>0&&chrome.runtime.sendMessage({type:"TEXT_SELECTED",payload:e})});chrome.runtime.onMessage.addListener((e,o,t)=>{if(e.type==="GET_SELECTED_TEXT"){const n=window.getSelection().toString().trim();t({selectedText:n})}return e.type==="SETTING_CHANGED"&&(console.log("SafeType: Setting changed:",e.setting,e.value),e.setting==="safeFilter"&&!e.value&&d(),e.setting==="zenMode"&&e.value?(r(),d(),v()):e.setting==="zenMode"&&!e.value&&h(),e.setting==="toneDetection"&&!e.value&&r()),!0});function _(e,o){d();const t=document.createElement("div");t.id="safetype-safe-filter-notification",t.innerHTML=`
        <div style="
            position: fixed;
            top: 20px;
            right: 20px;
            background: #fff;
            border: 2px solid #dc2626;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10001;
            max-width: 350px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            line-height: 1.4;
        ">
            <div style="display: flex; align-items: center; margin-bottom: 12px;">
                <span style="font-size: 20px; margin-right: 8px;">🚫</span>
                <strong style="color: #dc2626;">Content Filtered</strong>
            </div>
            <div style="margin-bottom: 12px; color: #374151;">
                ${o.content_issues||"Inappropriate content detected"}
            </div>
            ${o.suggested_alternative?`
                <div style="margin-bottom: 12px;">
                    <strong style="color: #059669;">Suggested alternative:</strong>
                    <div style="background: #f0fdf4; padding: 8px; border-radius: 6px; margin-top: 4px; border-left: 3px solid #059669;">
                        ${o.suggested_alternative}
                    </div>
                </div>
            `:""}
            <div style="display: flex; gap: 8px;">
                <button id="safetype-use-alternative" style="
                    background: #059669;
                    color: white;
                    border: none;
                    padding: 8px 12px;
                    border-radius: 6px;
                    cursor: pointer;
                    font-size: 12px;
                    font-weight: 500;
                ">Use Alternative</button>
                <button id="safetype-dismiss-filter" style="
                    background: #6b7280;
                    color: white;
                    border: none;
                    padding: 8px 12px;
                    border-radius: 6px;
                    cursor: pointer;
                    font-size: 12px;
                    font-weight: 500;
                ">Dismiss</button>
            </div>
        </div>
    `,document.body.appendChild(t),p=t;const n=document.getElementById("safetype-use-alternative"),i=document.getElementById("safetype-dismiss-filter");n&&o.suggested_alternative?n.addEventListener("click",()=>{m(e,o.suggested_alternative),d()}):n&&(n.style.display="none"),i&&i.addEventListener("click",()=>{d()}),setTimeout(()=>{d()},1e4)}function d(){p&&(p.remove(),p=null)}function m(e,o){try{console.log("SafeType: Replacing element text with:",o),e.focus(),e.tagName==="TEXTAREA"||e.tagName==="INPUT"?(e.value=o,e.dispatchEvent(new Event("input",{bubbles:!0})),e.dispatchEvent(new Event("change",{bubbles:!0}))):e.contentEditable==="true"&&(e.textContent=o,e.dispatchEvent(new Event("input",{bubbles:!0})),e.dispatchEvent(new Event("change",{bubbles:!0}))),console.log("SafeType: Text replacement successful")}catch(t){console.error("SafeType: Error replacing element text:",t)}}function F(e,o){r(),console.log("SafeType: Showing notification for element:",e);const t=document.createElement("div");t.id="safetype-confidence-notification",t.innerHTML=`
        <div style="
            position: fixed;
            top: 20px;
            right: 20px;
            background: #fff;
            border: 2px solid #f59e0b;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            max-width: 350px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            line-height: 1.4;
        ">
            <div style="display: flex; align-items: center; margin-bottom: 12px;">
                <span style="font-size: 20px; margin-right: 8px;">⚠️</span>
                <strong style="color: #d97706;">Low Confidence Detected</strong>
            </div>

            <div style="margin-bottom: 12px; color: #374151;">
                <div><strong>Confidence Score:</strong> ${o.score}/10</div>
                <div style="margin-top: 4px;"><strong>Issue:</strong> ${o.explanation}</div>
                <div style="margin-top: 4px;"><strong>Suggestion:</strong> ${o.improvements}</div>
            </div>

            <div style="margin-bottom: 12px; font-weight: 500; color: #374151;">
                Would you like me to rewrite this text with more confidence?
            </div>

            <div style="display: flex; gap: 8px;">
                <button id="safetype-rewrite-yes" style="
                    background: #4F46E5;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 6px;
                    cursor: pointer;
                    font-size: 13px;
                    font-weight: 500;
                ">Yes, Rewrite</button>

                <button id="safetype-rewrite-no" style="
                    background: #6b7280;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 6px;
                    cursor: pointer;
                    font-size: 13px;
                    font-weight: 500;
                ">No, Keep As Is</button>
            </div>
        </div>
    `,document.body.appendChild(t),a=t,document.getElementById("safetype-rewrite-yes").addEventListener("click",async()=>{console.log("SafeType: Yes button clicked!"),console.log("SafeType: Element to rewrite:",e),console.log("SafeType: Original text:",o.originalText);const n=document.getElementById("safetype-rewrite-yes");n&&(n.textContent="Rewriting...",n.disabled=!0);try{const i=await chrome.runtime.sendMessage({type:"REWRITE_TEXT",payload:o.originalText});if(i&&i.rewrittenText){if(console.log("SafeType: Got rewritten text:",i.rewrittenText),i.rewrittenText.startsWith("Error:")||i.rewrittenText.startsWith("Please set your")||i.rewrittenText.startsWith("API Error")){if(a){const s=a.querySelector("div");s.innerHTML=`
                            <div style="display: flex; align-items: center; margin-bottom: 12px;">
                                <span style="font-size: 20px; margin-right: 8px;">❌</span>
                                <strong style="color: #dc2626;">Error</strong>
                            </div>
                            <div style="margin-bottom: 12px; color: #374151;">
                                ${i.rewrittenText}
                            </div>
                            <button onclick="this.closest('#safetype-confidence-notification').remove()" style="
                                background: #6b7280;
                                color: white;
                                border: none;
                                padding: 8px 12px;
                                border-radius: 6px;
                                cursor: pointer;
                                font-size: 12px;
                            ">Close</button>
                        `}}else if(m(e,i.rewrittenText),a){const s=a.querySelector("div");s.innerHTML=`
                            <div style="display: flex; align-items: center; margin-bottom: 12px;">
                                <span style="font-size: 20px; margin-right: 8px;">✅</span>
                                <strong style="color: #059669;">Text Rewritten Successfully!</strong>
                            </div>
                            <div style="margin-bottom: 12px; color: #374151;">
                                Your text has been updated with a more confident tone.
                            </div>
                        `,setTimeout(()=>{r()},3e3)}}else console.error("SafeType: No rewritten text received"),r()}catch(i){console.error("SafeType: Error rewriting text:",i),r()}}),document.getElementById("safetype-rewrite-no").addEventListener("click",()=>{r()}),setTimeout(()=>{r()},15e3)}function r(){a&&(a.remove(),a=null)}function A(e,o){r();const t=document.createElement("div");t.id="safetype-confidence-notification",t.innerHTML=`
        <div style="
            position: fixed;
            top: 20px;
            right: 20px;
            background: #fff;
            border: 2px solid #10b981;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            max-width: 350px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            line-height: 1.4;
        ">
            <div style="display: flex; align-items: center; margin-bottom: 12px;">
                <span style="font-size: 20px; margin-right: 8px;">✅</span>
                <strong style="color: #059669;">Great Writing!</strong>
            </div>
            <div style="margin-bottom: 12px; color: #374151;">
                Your text sounds confident and clear. Score: ${o.score}/10
            </div>
            <div style="margin-bottom: 12px; color: #6b7280; font-size: 12px;">
                Mood: ${o.mood} • Tone: ${o.emotional_tone}
            </div>
            <button onclick="this.closest('#safetype-confidence-notification').remove()" style="
                background: #10b981;
                color: white;
                border: none;
                padding: 8px 12px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 12px;
                font-weight: 500;
            ">Thanks!</button>
        </div>
    `,document.body.appendChild(t),a=t,setTimeout(()=>{r()},5e3)}function v(){h();const e=document.createElement("div");e.id="safetype-zen-indicator",e.innerHTML=`
        <div style="
            position: fixed;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(99, 102, 241, 0.95);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 12px;
            font-weight: 500;
            z-index: 10002;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 6px;
        ">
            <span style="font-size: 14px;">🧘</span>
            <span>Zen Mode Active</span>
        </div>
    `,document.body.appendChild(e),l=e,setTimeout(()=>{l&&(l.style.opacity="0.6")},3e3)}function h(){l&&(l.remove(),l=null)}chrome.storage.sync.get(["zenMode"],function(e){e.zenMode===!0&&v()});y();const B=new MutationObserver(()=>{y()});B.observe(document.body,{childList:!0,subtree:!0});
})()
