<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SafeType</title>
    <style>
        body {
            width: 380px;
            min-height: 600px;
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1f2937;
        }

        .container {
            background: white;
            border-radius: 16px;
            margin: 8px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .header {
            display: flex;
            align-items: center;
            margin-bottom: 24px;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: #10b981;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: white;
            font-weight: bold;
            font-size: 18px;
        }

        .header-text h1 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
        }

        .header-text p {
            margin: 2px 0 0 0;
            font-size: 14px;
            color: #6b7280;
        }

        .toggle-section {
            margin-bottom: 32px;
        }

        .toggle-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f3f4f6;
        }

        .toggle-item:last-child {
            border-bottom: none;
        }

        .toggle-label {
            font-size: 16px;
            font-weight: 500;
            color: #374151;
        }

        .toggle-switch {
            position: relative;
            width: 48px;
            height: 24px;
            background: #d1d5db;
            border-radius: 12px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .toggle-switch.active {
            background: #10b981;
        }

        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .toggle-switch.active::after {
            transform: translateX(24px);
        }

        .rewrite-section {
            margin-bottom: 24px;
        }

        .section-header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }

        .section-icon {
            margin-right: 8px;
            font-size: 18px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #374151;
        }

        .text-input {
            width: 100%;
            min-height: 60px;
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            font-family: inherit;
            resize: vertical;
            margin-bottom: 12px;
            box-sizing: border-box;
        }

        .text-input:focus {
            outline: none;
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }

        .rewrite-button {
            width: 100%;
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .rewrite-button:hover {
            background: #2563eb;
        }

        .rewrite-button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }

        .feedback-message {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .feedback-message .emoji {
            font-size: 18px;
        }

        .feedback-message .text {
            font-size: 14px;
            color: #166534;
            font-weight: 500;
        }

        .mood-feedback {
            margin-bottom: 24px;
        }

        .mood-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 0;
        }

        .mood-left {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .mood-emoji {
            font-size: 20px;
        }

        .mood-text {
            font-size: 14px;
            color: #374151;
        }

        .mood-time {
            font-size: 12px;
            color: #9ca3af;
        }

        .hidden {
            display: none;
        }
    </style>
  <script type="module" crossorigin src="/assets/popup-6k74eCTX.js"></script>
  <link rel="modulepreload" crossorigin href="/assets/modulepreload-polyfill-B5Qt9EMX.js">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo-icon">✓</div>
            <div class="header-text">
                <h1>SafeType</h1>
                <p>Writing Confidence Assistant</p>
            </div>
        </div>

        <!-- Toggle Switches -->
        <div class="toggle-section">
            <div class="toggle-item">
                <span class="toggle-label">Real-Time Tone Detection</span>
                <div class="toggle-switch active" id="toneDetectionToggle"></div>
            </div>
            <div class="toggle-item">
                <span class="toggle-label">Safe Filter Mode</span>
                <div class="toggle-switch active" id="safeFilterToggle"></div>
            </div>
            <div class="toggle-item">
                <span class="toggle-label">Zen Mode</span>
                <div class="toggle-switch" id="zenModeToggle"></div>
            </div>
        </div>

        <!-- Rewrite Section -->
        <div class="rewrite-section">
            <div class="section-header">
                <span class="section-icon">↻</span>
                <span class="section-title">Rewrite Your Text</span>
            </div>
            <textarea class="text-input" id="inputText" placeholder="I'm not sure if this is ok"></textarea>
            <button class="rewrite-button" id="rewriteBtn">
                <span>Rewrite with Confidence</span>
                <span>↻</span>
            </button>
        </div>

        <!-- Feedback Message -->
        <div class="feedback-message" id="feedbackMessage">
            <span class="emoji">😊</span>
            <span class="text">This works well — go ahead!</span>
        </div>

        <!-- Recent Mood Feedback -->
        <div class="mood-feedback">
            <div class="section-header">
                <span class="section-icon">📈</span>
                <span class="section-title">Recent Mood Feedback</span>
            </div>
            <div id="moodHistory">
                <div class="mood-item">
                    <div class="mood-left">
                        <span class="mood-emoji">😰</span>
                        <span class="mood-text">Anxious — Gmail</span>
                    </div>
                    <span class="mood-time">2 mins ago</span>
                </div>
                <div class="mood-item">
                    <div class="mood-left">
                        <span class="mood-emoji">😊</span>
                        <span class="mood-text">Neutral — Discord</span>
                    </div>
                    <span class="mood-time">10 mins ago</span>
                </div>
            </div>
        </div>
    </div>

</body>
</html>
