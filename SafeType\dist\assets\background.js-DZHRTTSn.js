async function d(e){try{const t=(await chrome.storage.sync.get(["geminiApiKey"])).geminiApiKey;if(!t)return{error:"Please set your Gemini API key in the extension options."};const n={contents:[{parts:[{text:`Analyze the confidence level and emotional tone of this text on a scale of 1-10 (where 1 is very uncertain/hesitant and 10 is very confident/assertive). Also determine the mood and provide suggestions.

Text: "${e}"

Respond in this exact JSON format:
{
  "confidence_score": [number 1-10],
  "explanation": "[brief explanation of why this score]",
  "needs_rewriting": [true/false],
  "suggested_improvements": "[what could be improved]",
  "mood": "[one of: Confident, Neutral, Anxious, Excited, Frustrated, Sad, Happy]",
  "emotional_tone": "[brief description of the emotional tone detected]",
  "writing_style": "[formal/informal/casual/professional]"
}`}]}]},r=await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${t}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(n)});if(!r.ok)return{error:`API Error (${r.status})`};const i=(await r.json())?.candidates?.[0]?.content?.parts?.[0]?.text;if(!i)return{error:"No response from AI"};try{const s=i.match(/\{[\s\S]*\}/);return s?{success:!0,analysis:JSON.parse(s[0])}:{error:"Could not parse AI response"}}catch{return{error:"Invalid response format"}}}catch(o){return{error:o.message}}}async function u(e){try{const t=(await chrome.storage.sync.get(["geminiApiKey"])).geminiApiKey;if(!t)return{error:"Please set your Gemini API key in the extension options."};const n={contents:[{parts:[{text:`Analyze this text for inappropriate content including profanity, hate speech, harassment, threats, or other harmful language.

Text: "${e}"

Respond in this exact JSON format:
{
  "is_safe": [true/false],
  "content_issues": "[list any issues found, or empty string if safe]",
  "severity": "[low/medium/high if not safe, or 'none' if safe]",
  "suggested_alternative": "[cleaner version if not safe, or empty string if safe]"
}`}]}]},r=await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${t}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(n)});if(!r.ok)return{error:`API Error (${r.status})`};const i=(await r.json())?.candidates?.[0]?.content?.parts?.[0]?.text;if(!i)return{error:"No response from AI for safety check"};try{const s=i.match(/\{[\s\S]*\}/);return s?{success:!0,safetyCheck:JSON.parse(s[0])}:{error:"Could not parse safety check response"}}catch{return{error:"Invalid safety check response format"}}}catch(o){return{error:o.message}}}async function l(e){try{const t=(await chrome.storage.sync.get(["geminiApiKey"])).geminiApiKey;if(console.log("API Key exists:",!!t),!t)return"Please set your Gemini API key in the extension options.";if(!e||e.trim().length===0)return"Please provide some text to rewrite.";const n={contents:[{parts:[{text:`Rewrite the following text in a more confident and calming tone. Keep the same meaning but make it sound more assured and positive:

"${e}"`}]}]};console.log("Making API request to Gemini...");const r=await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${t}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(n)});if(console.log("Response status:",r.status),!r.ok){let s;try{s=await r.json(),console.error("API Error:",s)}catch(c){console.error("Failed to parse error response:",c),s={error:{message:"Failed to parse error response"}}}return r.status===400?"Invalid API request. Please check your API key.":r.status===403?"API key is invalid or doesn't have permission. Please check your Gemini API key.":r.status===429?"Too many requests. Please wait a moment and try again.":`API Error (${r.status}): ${s.error?.message||"Unknown error"}`}let a;try{a=await r.json(),console.log("API Response:",a)}catch(s){return console.error("Failed to parse API response:",s),"Failed to parse API response. Please try again."}if(a.error)return console.error("Gemini API Error:",a.error),`Error: ${a.error.message||"Unknown API error"}`;const i=a?.candidates?.[0]?.content?.parts?.[0]?.text;return i?i.trim():(console.error("No text in response:",a),"No response generated. The content might have been filtered.")}catch(o){return console.error("Rewrite function error:",o),`Error: ${o.message}`}}chrome.runtime.onInstalled.addListener(()=>{chrome.contextMenus.create({id:"rewriteText",title:"Rewrite with SafeType",contexts:["selection"]}),chrome.storage.sync.get(["toneDetection","safeFilter","zenMode"],function(e){const o={toneDetection:e.toneDetection!==void 0?e.toneDetection:!0,safeFilter:e.safeFilter!==void 0?e.safeFilter:!0,zenMode:e.zenMode!==void 0?e.zenMode:!1};chrome.storage.sync.set(o),console.log("SafeType: Settings initialized:",o)}),chrome.storage.local.get(["moodHistory"],function(e){e.moodHistory||(chrome.storage.local.set({moodHistory:[]}),console.log("SafeType: Mood history initialized"))})});chrome.contextMenus.onClicked.addListener((e,o)=>{e.menuItemId==="rewriteText"&&e.selectionText&&l(e.selectionText).then(t=>{chrome.scripting.executeScript({target:{tabId:o.id},func:f,args:[t]})})});function f(e){const o=window.getSelection();if(o.rangeCount>0){const t=o.getRangeAt(0);t.deleteContents(),t.insertNode(document.createTextNode(e)),o.removeAllRanges()}}chrome.runtime.onMessage.addListener((e,o,t)=>{if(e.type==="REWRITE_TEXT")return l(e.payload).then(n=>{t({rewrittenText:n})}),!0;if(e.type==="ANALYZE_CONFIDENCE")return d(e.payload).then(n=>{t(n)}),!0;if(e.type==="CHECK_SAFE_CONTENT")return u(e.payload).then(n=>{t(n)}),!0;if(e.type==="GET_SETTINGS")return chrome.storage.sync.get(["toneDetection","safeFilter","zenMode"],function(n){t(n)}),!0;if(e.type==="UPDATE_SETTING"){const{setting:n,value:r}=e.payload;return chrome.storage.sync.set({[n]:r},function(){console.log(`SafeType: Setting ${n} updated to ${r}`),chrome.tabs.query({},function(a){a.forEach(i=>{chrome.tabs.sendMessage(i.id,{type:"SETTING_CHANGED",setting:n,value:r}).catch(()=>{})})}),t({success:!0})}),!0}if(e.type==="CLEAR_MOOD_HISTORY")return chrome.storage.local.set({moodHistory:[]},function(){console.log("SafeType: Mood history cleared"),t({success:!0})}),!0});
