# SafeType Chrome Extension

A comprehensive confidence-boosting writing assistant that analyzes your text in real-time, provides mood feedback, filters inappropriate content, and helps you write with more confidence using Google's Gemini AI.

## ✨ Features

### Core Functionality
- **🔍 Real-Time Tone Detection**: Automatically analyzes confidence and mood while typing
- **⚡ Smart Notifications**: Shows helpful suggestions when text could be more confident
- **✍️ One-Click Rewriting**: Yes/No buttons to instantly improve your text
- **🌐 Universal Compatibility**: Works in Gmail, Discord, Slack, social media, and any text input
- **📊 Confidence Scoring**: Get detailed analysis with scores from 1-10

### New Advanced Features
- **🛡️ Safe Filter Mode**: Detects and filters inappropriate content with suggested alternatives
- **😊 Mood Analysis & Tracking**: Real-time emotional tone detection with history tracking
- **🧘 Zen Mode**: Distraction-free writing with minimal notifications
- **📈 Recent Mood Feedback**: Visual history of your writing mood across different platforms
- **🎯 Modern Popup Interface**: Clean, intuitive design with toggle controls
- **🔒 Persistent Settings**: All preferences saved and synced across browser sessions

### Interface & Usability
- **🎨 Modern UI Design**: Beautiful interface matching contemporary design standards
- **🔄 Toggle Controls**: Easy-to-use switches for all features
- **📱 Responsive Design**: Optimized for different screen sizes
- **⚙️ Settings Management**: Comprehensive options and preferences
- **🖱️ Context Menu**: Right-click on selected text to rewrite it instantly

## 🚀 How It Works

1. **Type anywhere** - Gmail, WhatsApp Web, Twitter, LinkedIn, etc.
2. **Get instant feedback** - Extension analyzes confidence level as you type
3. **See smart notifications** - Get alerts when text could be more confident
4. **One-click improvement** - Click "Yes" to automatically rewrite with confidence
5. **Keep or discard** - Choose to keep the original or use the improved version

## 📋 Setup Instructions

### 1. Get a Gemini API Key
1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a free API key
3. Copy the API key for later use

### 2. Build the Extension
```bash
npm install
npm run build
```

### 3. Load the Extension in Chrome
1. Open Chrome and go to `chrome://extensions/`
2. Enable "Developer mode" in the top right
3. Click "Load unpacked"
4. Select the `dist` folder that was created after building

### 4. Configure the Extension
1. Right-click the SafeType extension icon
2. Select "Options"
3. Enter your Gemini API key
4. Click "Save Settings"

## 💡 Usage Examples

### Real-time Analysis While Typing
- Type in any text field (Gmail, Discord, LinkedIn, etc.)
- Extension automatically analyzes confidence and mood after 1.5 seconds
- Get notifications when text could be more confident
- Click "Yes, Rewrite" to improve instantly
- View mood history in the popup interface

### Safe Filter Mode
- Enable Safe Filter in the popup to automatically detect inappropriate content
- Get instant notifications when problematic language is detected
- Receive suggested alternatives for cleaner communication
- Perfect for professional environments and family-friendly usage

### Zen Mode for Focused Writing
- Enable Zen Mode to minimize distractions while writing
- Mood tracking continues in the background without notifications
- Subtle indicator shows when Zen Mode is active
- Ideal for deep work and concentrated writing sessions

### Manual Analysis in Popup
1. Click the SafeType extension icon
2. Enter or paste your text
3. Click "Check Confidence" to analyze
4. Click "Rewrite Text" to improve

### Context Menu
1. Select any text on a webpage
2. Right-click and select "Rewrite with SafeType"
3. Text gets replaced with more confident version

## 🎯 Perfect For

- **Email writing** (Gmail, Outlook)
- **Social media posts** (Twitter, LinkedIn, Facebook)
- **Chat applications** (WhatsApp Web, Slack, Discord)
- **Professional communication**
- **Academic writing**
- **Any text input on the web**
