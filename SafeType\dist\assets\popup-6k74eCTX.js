import"./modulepreload-polyfill-B5Qt9EMX.js";document.addEventListener("DOMContentLoaded",function(){const d=document.getElementById("inputText"),c=document.getElementById("rewriteBtn");document.getElementById("feedbackMessage"),document.getElementById("moodHistory");const f=document.getElementById("toneDetectionToggle"),u=document.getElementById("safeFilterToggle"),m=document.getElementById("zenModeToggle");v(),M(),f.addEventListener("click",function(){y(f,"toneDetection")}),u.addEventListener("click",function(){y(u,"safeFilter")}),m.addEventListener("click",function(){y(m,"zenMode")}),c.addEventListener("click",async function(){const t=d.value.trim();if(!t){s("⚠️","Please enter some text to rewrite.","warning");return}if((await w()).safeFilter&&await h(t)){s("🚫","Content filtered: Please use appropriate language.","error");return}c.disabled=!0,c.innerHTML="<span>Rewriting...</span><span>⏳</span>";try{const e=await chrome.runtime.sendMessage({type:"REWRITE_TEXT",payload:t});e&&e.rewrittenText?e.rewrittenText.startsWith("Error:")||e.rewrittenText.startsWith("Please set your")||e.rewrittenText.startsWith("API Error")||e.rewrittenText.startsWith("Invalid")||e.rewrittenText.startsWith("Too many")?s("❌",e.rewrittenText,"error"):(d.value=e.rewrittenText,s("✅","Text rewritten successfully!","success"),p("Confident","Popup","😊")):s("❌","No response received. Please check your API key.","error")}catch(e){console.error("Popup Error:",e),s("❌",`Connection error: ${e.message}`,"error")}finally{c.disabled=!1,c.innerHTML="<span>Rewrite with Confidence</span><span>↻</span>"}});let T;d.addEventListener("input",function(){clearTimeout(T);const t=d.value.trim();t.length>10?T=setTimeout(()=>{b(t)},1500):s("💭","Type something to get started...","neutral")});async function b(t){try{if((await w()).safeFilter&&await h(t)){s("🚫","Content filtered: Please use appropriate language.","error"),p("Filtered","Popup","🚫");return}const e=await chrome.runtime.sendMessage({type:"ANALYZE_CONFIDENCE",payload:t});if(e.success&&e.analysis){const{confidence_score:o,explanation:a,needs_rewriting:r}=e.analysis;let i,l,g;o>=7?(i="Confident",l="😊",g="This works well — go ahead!"):o>=5?(i="Neutral",l="😐",g="This is okay, but could be more confident."):(i="Anxious",l="😰",g="This text seems uncertain. Consider rewriting."),s(l,g,i.toLowerCase()),p(i,"Popup",l)}else e.error&&s("❌",e.error,"error")}catch(n){console.error("Analysis Error:",n),s("❌","Analysis failed. Please try again.","error")}}function s(t,n,e){const o=document.getElementById("feedbackMessage"),a=o.querySelector(".emoji"),r=o.querySelector(".text");a.textContent=t,r.textContent=n,o.className="feedback-message",e==="error"?(o.style.background="#fef2f2",o.style.borderColor="#fecaca",r.style.color="#dc2626"):e==="warning"?(o.style.background="#fffbeb",o.style.borderColor="#fed7aa",r.style.color="#d97706"):e==="success"?(o.style.background="#f0fdf4",o.style.borderColor="#bbf7d0",r.style.color="#166534"):(o.style.background="#f8fafc",o.style.borderColor="#e2e8f0",r.style.color="#475569")}function y(t,n){t.classList.toggle("active");const e=t.classList.contains("active");chrome.runtime.sendMessage({type:"UPDATE_SETTING",payload:{setting:n,value:e}},function(o){o&&o.success&&(console.log(`SafeType: ${n} setting updated to ${e}`),n==="zenMode"&&e?s("🧘","Zen Mode enabled - minimal notifications","success"):n==="safeFilter"&&e?s("🛡️","Safe Filter enabled - content will be filtered","success"):n==="toneDetection"&&e&&s("🎯","Real-time tone detection enabled","success"))})}async function v(){try{const t=await chrome.runtime.sendMessage({type:"GET_SETTINGS"});t.toneDetection!==!1&&f.classList.add("active"),t.safeFilter!==!1&&u.classList.add("active"),t.zenMode===!0&&m.classList.add("active"),console.log("SafeType: Settings loaded:",t)}catch(t){console.error("SafeType: Error loading settings:",t),f.classList.add("active"),u.classList.add("active")}}async function w(){return await chrome.storage.sync.get(["toneDetection","safeFilter","zenMode"])}async function h(t){const n=["damn","hell","shit","fuck","bitch","ass","crap","piss","stupid","idiot","moron","dumb","hate","kill","die"],e=t.toLowerCase();return n.some(o=>e.includes(o))}function p(t,n,e){const o=new Date,a="just now";chrome.storage.local.get(["moodHistory"],function(r){const i=r.moodHistory||[];i.unshift({mood:t,source:n,emoji:e,timestamp:o.toISOString(),timeAgo:a}),i.length>10&&i.splice(10),chrome.storage.local.set({moodHistory:i}),E(i)})}function M(){chrome.storage.local.get(["moodHistory"],function(t){const n=t.moodHistory||[];E(n)})}function E(t){const n=document.getElementById("moodHistory");if(t.length===0){n.innerHTML='<div style="text-align: center; color: #9ca3af; padding: 20px;">No recent activity</div>';return}n.innerHTML=t.map(e=>{const o=x(new Date(e.timestamp));return`
                <div class="mood-item">
                    <div class="mood-left">
                        <span class="mood-emoji">${e.emoji}</span>
                        <span class="mood-text">${e.mood} — ${e.source}</span>
                    </div>
                    <span class="mood-time">${o}</span>
                </div>
            `}).join("")}function x(t){const e=new Date-t,o=Math.floor(e/6e4),a=Math.floor(e/36e5),r=Math.floor(e/864e5);return o<1?"just now":o<60?`${o} min${o>1?"s":""} ago`:a<24?`${a} hour${a>1?"s":""} ago`:`${r} day${r>1?"s":""} ago`}s("💭","Type something to get started...","neutral")});
