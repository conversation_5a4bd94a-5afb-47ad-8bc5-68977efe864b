<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SafeType Options</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 40px auto;
            padding: 20px;
            background: #f8fafc;
        }
        
        .container {
            background: white;
            padding: 32px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: #1f2937;
            margin-bottom: 8px;
        }
        
        .subtitle {
            color: #6b7280;
            margin-bottom: 32px;
        }
        
        .form-group {
            margin-bottom: 24px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #374151;
        }
        
        input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .help-text {
            font-size: 12px;
            color: #6b7280;
            margin-top: 4px;
        }
        
        .help-text a {
            color: #4F46E5;
            text-decoration: none;
        }
        
        .help-text a:hover {
            text-decoration: underline;
        }
        
        button {
            background: #4F46E5;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
        }
        
        button:hover {
            background: #4338ca;
        }
        
        .status {
            margin-top: 16px;
            padding: 12px;
            border-radius: 8px;
            font-size: 14px;
        }
        
        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        
        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        
        .hidden {
            display: none;
        }
    </style>
  <script type="module" crossorigin src="/assets/options-BkZcIfdR.js"></script>
  <link rel="modulepreload" crossorigin href="/assets/modulepreload-polyfill-B5Qt9EMX.js">
</head>
<body>
    <div class="container">
        <h1>SafeType Options</h1>
        <p class="subtitle">Configure your SafeType extension settings</p>
        
        <form id="optionsForm">
            <div class="form-group">
                <label for="apiKey">Gemini API Key:</label>
                <input type="password" id="apiKey" placeholder="Enter your Gemini API key">
                <div class="help-text">
                    Get your free API key from <a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a>
                </div>
            </div>
            
            <button type="submit">Save Settings</button>
        </form>
        
        <div id="status" class="status hidden"></div>
    </div>
    
</body>
</html>
