(function(){console.log("SafeType content script loaded");let g=null,f=null,s=null,u=null,c=null;function y(){["textarea",'input[type="text"]','input[type="email"]','[contenteditable="true"]','[role="textbox"]',".editable",'[data-testid*="compose"]','[data-testid*="message"]','div[contenteditable="true"][role="textbox"]','div[contenteditable="true"][aria-label*="Message"]','div[contenteditable="true"][aria-label*="message"]','div[contenteditable="true"][aria-label*="compose"]','div[contenteditable="true"][aria-label*="reply"]','div[contenteditable="true"][aria-label*="forward"]','div[contenteditable="true"][data-tab]','div[contenteditable="true"][role="textbox"][spellcheck="true"]','div[contenteditable="true"][aria-label*="Tweet"]','div[contenteditable="true"][aria-label*="Post"]','div[contenteditable="true"][aria-label*="Write"]','div[contenteditable="true"][role="textbox"][aria-multiline="true"]','div[contenteditable="true"][role="textbox"][aria-label*="Message"]','div[contenteditable="true"][data-qa="message_input"]','div[contenteditable="true"][aria-label*="Write"]','div[contenteditable="true"][role="textbox"][aria-describedby]'].forEach(o=>{document.querySelectorAll(o).forEach(t=>{t.hasAttribute("data-safetype-monitored")||(t.setAttribute("data-safetype-monitored","true"),t.addEventListener("input",M),t.addEventListener("focus",z),t.addEventListener("blur",C))})})}function z(e){g=e.target}function C(e){g===e.target&&(g=null,a())}function M(e){const o=e.target,t=I(o);f&&clearTimeout(f),t.length>10?f=setTimeout(()=>{N(t,o)},2e3):a()}function I(e){return e.tagName==="TEXTAREA"||e.tagName==="INPUT"?e.value:e.contentEditable==="true"?e.innerText||e.textContent:""}async function N(e,o){try{const t=await chrome.storage.sync.get(["toneDetection","safeFilter","zenMode"]);if(t.toneDetection===!1)return;if(t.safeFilter!==!1){const r=await chrome.runtime.sendMessage({type:"CHECK_SAFE_CONTENT",payload:e});if(r.success&&r.safetyCheck&&!r.safetyCheck.is_safe){L(o,r.safetyCheck),m("Filtered",b(),"🚫");return}}const i=await chrome.runtime.sendMessage({type:"ANALYZE_CONFIDENCE",payload:e});if(i.success&&i.analysis){const{confidence_score:r,needs_rewriting:n,explanation:d,suggested_improvements:E,mood:T,emotional_tone:x,writing_style:S}=i.analysis,p=T||(r>=7?"Confident":r>=5?"Neutral":"Anxious"),k=A(p);m(p,b(),k),t.zenMode!==!0?r<6&&n?F(o,{score:r,explanation:d,improvements:E,originalText:e,mood:p,emotional_tone:x,writing_style:S}):r>=8?_(o,{score:r,mood:p,emotional_tone:x}):a():a()}}catch(t){console.error("SafeType: Error analyzing confidence:",t)}}function A(e){return{Confident:"😊",Neutral:"😐",Anxious:"😰",Excited:"🤩",Frustrated:"😤",Sad:"😢",Happy:"😄",Filtered:"🚫"}[e]||"😐"}function b(){const e=window.location.hostname;return e.includes("gmail.com")?"Gmail":e.includes("discord.com")?"Discord":e.includes("slack.com")?"Slack":e.includes("twitter.com")||e.includes("x.com")?"Twitter":e.includes("linkedin.com")?"LinkedIn":e.includes("facebook.com")?"Facebook":"Web"}function m(e,o,t){const i=new Date;chrome.storage.local.get(["moodHistory"],function(r){const n=r.moodHistory||[];n.unshift({mood:e,source:o,emoji:t,timestamp:i.toISOString()}),n.length>20&&n.splice(20),chrome.storage.local.set({moodHistory:n})})}document.addEventListener("mouseup",function(){const e=window.getSelection().toString().trim();e.length>0&&chrome.runtime.sendMessage({type:"TEXT_SELECTED",payload:e})});chrome.runtime.onMessage.addListener((e,o,t)=>{if(e.type==="GET_SELECTED_TEXT"){const i=window.getSelection().toString().trim();t({selectedText:i})}return e.type==="SETTING_CHANGED"&&(console.log("SafeType: Setting changed:",e.setting,e.value),e.setting==="safeFilter"&&!e.value&&l(),e.setting==="zenMode"&&e.value?(a(),l(),h()):e.setting==="zenMode"&&!e.value&&w(),e.setting==="toneDetection"&&!e.value&&a()),!0});function L(e,o){l();const t=document.createElement("div");t.id="safetype-safe-filter-notification",t.innerHTML=`
        <div style="
            position: fixed;
            top: 20px;
            right: 20px;
            background: #fff;
            border: 2px solid #dc2626;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10001;
            max-width: 350px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            line-height: 1.4;
        ">
            <div style="display: flex; align-items: center; margin-bottom: 12px;">
                <span style="font-size: 20px; margin-right: 8px;">🚫</span>
                <strong style="color: #dc2626;">Content Filtered</strong>
            </div>
            <div style="margin-bottom: 12px; color: #374151;">
                ${o.content_issues||"Inappropriate content detected"}
            </div>
            ${o.suggested_alternative?`
                <div style="margin-bottom: 12px;">
                    <strong style="color: #059669;">Suggested alternative:</strong>
                    <div style="background: #f0fdf4; padding: 8px; border-radius: 6px; margin-top: 4px; border-left: 3px solid #059669;">
                        ${o.suggested_alternative}
                    </div>
                </div>
            `:""}
            <div style="display: flex; gap: 8px;">
                <button id="safetype-use-alternative" style="
                    background: #059669;
                    color: white;
                    border: none;
                    padding: 8px 12px;
                    border-radius: 6px;
                    cursor: pointer;
                    font-size: 12px;
                    font-weight: 500;
                ">Use Alternative</button>
                <button id="safetype-dismiss-filter" style="
                    background: #6b7280;
                    color: white;
                    border: none;
                    padding: 8px 12px;
                    border-radius: 6px;
                    cursor: pointer;
                    font-size: 12px;
                    font-weight: 500;
                ">Dismiss</button>
            </div>
        </div>
    `,document.body.appendChild(t),u=t;const i=document.getElementById("safetype-use-alternative"),r=document.getElementById("safetype-dismiss-filter");i&&o.suggested_alternative?i.addEventListener("click",()=>{v(e,o.suggested_alternative),l()}):i&&(i.style.display="none"),r&&r.addEventListener("click",()=>{l()}),setTimeout(()=>{l()},1e4)}function l(){u&&(u.remove(),u=null)}function v(e,o){try{if(console.log("SafeType: Replacing element text with:",o),e.focus(),e.tagName==="TEXTAREA"||e.tagName==="INPUT")e.value=o,e.dispatchEvent(new Event("input",{bubbles:!0})),e.dispatchEvent(new Event("change",{bubbles:!0}));else if(e.contentEditable==="true"){const t=document.createRange();t.selectNodeContents(e);const i=window.getSelection();i.removeAllRanges(),i.addRange(t),document.execCommand?document.execCommand("insertText",!1,o):e.textContent=o,e.dispatchEvent(new Event("input",{bubbles:!0})),e.dispatchEvent(new Event("change",{bubbles:!0})),e.dispatchEvent(new InputEvent("input",{bubbles:!0,cancelable:!0,data:o,inputType:"insertText"}))}console.log("SafeType: Text replacement successful")}catch(t){console.error("SafeType: Error replacing element text:",t)}}function F(e,o){a(),console.log("SafeType: Showing notification for element:",e);const t=document.createElement("div");t.id="safetype-confidence-notification",t.innerHTML=`
        <div style="
            position: fixed;
            top: 20px;
            right: 20px;
            background: #fff;
            border: 2px solid #f59e0b;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            max-width: 350px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            line-height: 1.4;
        ">
            <div style="display: flex; align-items: center; margin-bottom: 12px;">
                <span style="font-size: 20px; margin-right: 8px;">⚠️</span>
                <strong style="color: #d97706;">Low Confidence Detected</strong>
            </div>

            <div style="margin-bottom: 12px; color: #374151;">
                <div><strong>Confidence Score:</strong> ${o.score}/10</div>
                <div style="margin-top: 4px;"><strong>Issue:</strong> ${o.explanation}</div>
                <div style="margin-top: 4px;"><strong>Suggestion:</strong> ${o.improvements}</div>
            </div>

            <div style="margin-bottom: 12px; font-weight: 500; color: #374151;">
                Would you like me to rewrite this text with more confidence?
            </div>

            <div style="display: flex; gap: 8px;">
                <button id="safetype-rewrite-yes" style="
                    background: #4F46E5;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 6px;
                    cursor: pointer;
                    font-size: 13px;
                    font-weight: 500;
                ">Yes, Rewrite</button>

                <button id="safetype-rewrite-no" style="
                    background: #6b7280;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 6px;
                    cursor: pointer;
                    font-size: 13px;
                    font-weight: 500;
                ">No, Keep As Is</button>
            </div>
        </div>
    `,document.body.appendChild(t),s=t,document.getElementById("safetype-rewrite-yes").addEventListener("click",async()=>{console.log("SafeType: Yes button clicked!"),console.log("SafeType: Element to rewrite:",e),console.log("SafeType: Original text:",o.originalText);const i=document.getElementById("safetype-rewrite-yes"),r=document.getElementById("safetype-rewrite-no");i&&(i.textContent="Rewriting...",i.disabled=!0),r&&(r.disabled=!0);try{const n=await chrome.runtime.sendMessage({type:"REWRITE_TEXT",payload:o.originalText});if(console.log("SafeType: Received response:",n),n&&n.rewrittenText){if(console.log("SafeType: Got rewritten text:",n.rewrittenText),n.rewrittenText.startsWith("Error:")||n.rewrittenText.startsWith("Please set your")||n.rewrittenText.startsWith("API Error")||n.rewrittenText.includes("API key")){if(s){const d=s.querySelector("div");d.innerHTML=`
                            <div style="display: flex; align-items: center; margin-bottom: 12px;">
                                <span style="font-size: 20px; margin-right: 8px;">❌</span>
                                <strong style="color: #dc2626;">Error</strong>
                            </div>
                            <div style="margin-bottom: 12px; color: #374151;">
                                ${n.rewrittenText}
                            </div>
                            <button onclick="this.closest('#safetype-confidence-notification').remove()" style="
                                background: #6b7280;
                                color: white;
                                border: none;
                                padding: 8px 12px;
                                border-radius: 6px;
                                cursor: pointer;
                                font-size: 12px;
                            ">Close</button>
                        `}}else if(console.log("SafeType: Attempting to replace text in element"),v(e,n.rewrittenText),s){const d=s.querySelector("div");d.innerHTML=`
                            <div style="display: flex; align-items: center; margin-bottom: 12px;">
                                <span style="font-size: 20px; margin-right: 8px;">✅</span>
                                <strong style="color: #059669;">Text Rewritten Successfully!</strong>
                            </div>
                            <div style="margin-bottom: 12px; color: #374151;">
                                Your text has been updated with a more confident tone.
                            </div>
                        `,setTimeout(()=>{a()},3e3)}}else if(console.error("SafeType: No rewritten text received or response is null"),s){const d=s.querySelector("div");d.innerHTML=`
                        <div style="display: flex; align-items: center; margin-bottom: 12px;">
                            <span style="font-size: 20px; margin-right: 8px;">❌</span>
                            <strong style="color: #dc2626;">Error</strong>
                        </div>
                        <div style="margin-bottom: 12px; color: #374151;">
                            Failed to get rewritten text. Please try again.
                        </div>
                        <button onclick="this.closest('#safetype-confidence-notification').remove()" style="
                            background: #6b7280;
                            color: white;
                            border: none;
                            padding: 8px 12px;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 12px;
                        ">Close</button>
                    `}}catch(n){if(console.error("SafeType: Error rewriting text:",n),s){const d=s.querySelector("div");d.innerHTML=`
                    <div style="display: flex; align-items: center; margin-bottom: 12px;">
                        <span style="font-size: 20px; margin-right: 8px;">❌</span>
                        <strong style="color: #dc2626;">Error</strong>
                    </div>
                    <div style="margin-bottom: 12px; color: #374151;">
                        An error occurred: ${n.message}
                    </div>
                    <button onclick="this.closest('#safetype-confidence-notification').remove()" style="
                        background: #6b7280;
                        color: white;
                        border: none;
                        padding: 8px 12px;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 12px;
                    ">Close</button>
                `}}}),document.getElementById("safetype-rewrite-no").addEventListener("click",()=>{a()}),setTimeout(()=>{a()},15e3)}function a(){s&&(s.remove(),s=null)}function _(e,o){a();const t=document.createElement("div");t.id="safetype-confidence-notification",t.innerHTML=`
        <div style="
            position: fixed;
            top: 20px;
            right: 20px;
            background: #fff;
            border: 2px solid #10b981;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            max-width: 350px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            line-height: 1.4;
        ">
            <div style="display: flex; align-items: center; margin-bottom: 12px;">
                <span style="font-size: 20px; margin-right: 8px;">✅</span>
                <strong style="color: #059669;">Great Writing!</strong>
            </div>
            <div style="margin-bottom: 12px; color: #374151;">
                Your text sounds confident and clear. Score: ${o.score}/10
            </div>
            <div style="margin-bottom: 12px; color: #6b7280; font-size: 12px;">
                Mood: ${o.mood} • Tone: ${o.emotional_tone}
            </div>
            <button onclick="this.closest('#safetype-confidence-notification').remove()" style="
                background: #10b981;
                color: white;
                border: none;
                padding: 8px 12px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 12px;
                font-weight: 500;
            ">Thanks!</button>
        </div>
    `,document.body.appendChild(t),s=t,setTimeout(()=>{a()},5e3)}function h(){w();const e=document.createElement("div");e.id="safetype-zen-indicator",e.innerHTML=`
        <div style="
            position: fixed;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(99, 102, 241, 0.95);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 12px;
            font-weight: 500;
            z-index: 10002;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 6px;
        ">
            <span style="font-size: 14px;">🧘</span>
            <span>Zen Mode Active</span>
        </div>
    `,document.body.appendChild(e),c=e,setTimeout(()=>{c&&(c.style.opacity="0.6")},3e3)}function w(){c&&(c.remove(),c=null)}chrome.storage.sync.get(["zenMode"],function(e){e.zenMode===!0&&h()});y();const R=new MutationObserver(()=>{y()});R.observe(document.body,{childList:!0,subtree:!0});
})()
